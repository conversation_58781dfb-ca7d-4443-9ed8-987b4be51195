import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { purchaseOrderAPI } from '../api/api';

const TruckerDashboard = () => {
  const { user, logout } = useAuth();
  const [purchaseOrders, setPurchaseOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchMyPOs();
  }, []);

  const fetchMyPOs = async () => {
    try {
      setLoading(true);
      const response = await purchaseOrderAPI.getMyPOs();
      setPurchaseOrders(response.data);
    } catch (error) {
      setError('Failed to fetch your purchase orders');
      console.error('Fetch my POs error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMarkCompleted = async (poId) => {
    try {
      await purchaseOrderAPI.updateStatus(poId);
      fetchMyPOs(); // Refresh the list
    } catch (error) {
      setError('Failed to mark purchase order as completed');
      console.error('Update status error:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Trucker Dashboard</h1>
              <p className="text-gray-600">Welcome, {user?.name}</p>
            </div>
            <button
              onClick={logout}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Logout
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {error && (
          <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {/* My Purchase Orders */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold">My Assigned Purchase Orders</h2>
          </div>
          
          {purchaseOrders.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No purchase orders assigned to you yet
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {purchaseOrders.map((po) => (
                <div key={po._id} className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        {po.po_number}
                      </h3>
                      <p className="text-sm text-gray-600">
                        Client: {po.client_name}
                      </p>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                        po.status === 'Assigned' ? 'bg-blue-100 text-blue-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {po.status}
                      </span>
                      {po.status === 'Assigned' && (
                        <button
                          onClick={() => handleMarkCompleted(po._id)}
                          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                        >
                          Mark Completed
                        </button>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-sm font-medium text-gray-700">Pickup Location:</p>
                      <p className="text-sm text-gray-600">{po.pickup_location}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Total Amount:</p>
                      <p className="text-sm text-gray-600">${po.total.toFixed(2)}</p>
                    </div>
                  </div>

                  {po.notes && (
                    <div className="mb-4">
                      <p className="text-sm font-medium text-gray-700">Notes:</p>
                      <p className="text-sm text-gray-600">{po.notes}</p>
                    </div>
                  )}

                  {/* Line Items */}
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">Line Items:</p>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                              Material
                            </th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                              Quantity
                            </th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                              Unit Price
                            </th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                              Hauling Rate
                            </th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                              Dropoff Location
                            </th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                              Subtotal
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {po.line_items.map((item, index) => (
                            <tr key={index}>
                              <td className="px-4 py-2 text-sm text-gray-900">
                                {item.material_name}
                              </td>
                              <td className="px-4 py-2 text-sm text-gray-500">
                                {item.quantity}
                              </td>
                              <td className="px-4 py-2 text-sm text-gray-500">
                                ${item.unit_price.toFixed(2)}
                              </td>
                              <td className="px-4 py-2 text-sm text-gray-500">
                                ${item.hauling_rate.toFixed(2)}
                              </td>
                              <td className="px-4 py-2 text-sm text-gray-500">
                                {item.dropoff_location}
                              </td>
                              <td className="px-4 py-2 text-sm text-gray-500">
                                ${(item.quantity * (item.unit_price + item.hauling_rate)).toFixed(2)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TruckerDashboard;
