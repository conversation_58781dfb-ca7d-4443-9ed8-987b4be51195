import axios from 'axios';

const API_BASE_URL = 'http://localhost:5000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests if available
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle token expiration
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/';
    }
    return Promise.reject(error);
  }
);

// Auth API calls
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
};

// Purchase Order API calls
export const purchaseOrderAPI = {
  // Admin endpoints
  createPO: (poData) => api.post('/purchase-orders', poData),
  getAllPOs: () => api.get('/purchase-orders'),
  getTruckers: () => api.get('/purchase-orders/truckers'),
  assignPO: (id, truckerData) => api.put(`/purchase-orders/${id}/assign`, truckerData),

  // Trucker endpoints
  getMyPOs: () => api.get('/purchase-orders/my'),
  updateStatus: (id) => api.put(`/purchase-orders/${id}/status`),
};

export default api;
