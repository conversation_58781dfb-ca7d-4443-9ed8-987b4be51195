require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/User');
const connectDB = require('./config/db');

const seedUsers = async () => {
  try {
    await connectDB();
    
    // Clear existing users
    await User.deleteMany({});
    
    // Create demo users
    const users = [
      {
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'password',
        role: 'admin'
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'password',
        role: 'trucker'
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'password',
        role: 'trucker'
      }
    ];
    
    for (const userData of users) {
      const user = new User(userData);
      await user.save();
      console.log(`Created user: ${user.name} (${user.email}) - ${user.role}`);
    }
    
    console.log('Demo users created successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding users:', error);
    process.exit(1);
  }
};

seedUsers();
