const express = require('express');
const {
  createPO,
  getAllPOs,
  assignPO,
  getMyPOs,
  updateStatus,
  getTruckers
} = require('../controllers/poController');
const { protect, adminOnly, truckerOnly } = require('../middlewares/authMiddleware');

const router = express.Router();

// POST /api/purchase-orders - Create new PO (Admin only)
router.post('/', protect, adminOnly, createPO);

// GET /api/purchase-orders - Get all POs (Admin only)
router.get('/', protect, adminOnly, getAllPOs);

// GET /api/purchase-orders/truckers - Get all truckers (Admin only)
router.get('/truckers', protect, adminOnly, getTruckers);

// PUT /api/purchase-orders/:id/assign - Assign PO to trucker (Admin only)
router.put('/:id/assign', protect, adminOnly, assignPO);

// GET /api/purchase-orders/my - Get trucker's assigned POs (Trucker only)
router.get('/my', protect, truckerOnly, getMyPOs);

// PUT /api/purchase-orders/:id/status - Update PO status (Trucker only)
router.put('/:id/status', protect, truckerOnly, updateStatus);

module.exports = router;
