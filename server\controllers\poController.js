const PurchaseOrder = require('../models/PurchaseOrder');
const User = require('../models/User');

// @desc    Create new purchase order
// @route   POST /api/purchase-orders
// @access  Private (Admin only)
const createPO = async (req, res) => {
  try {
    const { client_name, pickup_location, line_items, notes } = req.body;

    // Validate line items
    if (!line_items || line_items.length === 0) {
      return res.status(400).json({ message: 'At least one line item is required' });
    }

    // Calculate total
    const total = line_items.reduce((sum, item) => {
      return sum + (item.quantity * (item.unit_price + item.hauling_rate));
    }, 0);

    // Get current count for PO number generation
    const count = await PurchaseOrder.countDocuments();
    const po_number = `PO-${count + 1}`;

    // Create purchase order
    const purchaseOrder = await PurchaseOrder.create({
      po_number,
      client_name,
      pickup_location,
      line_items,
      total,
      notes: notes || '',
      status: 'Pending',
      assigned_to: null
    });

    res.status(201).json(purchaseOrder);
  } catch (error) {
    console.error('Create PO error:', error);
    res.status(500).json({ message: 'Server error creating purchase order' });
  }
};

// @desc    Get all purchase orders
// @route   GET /api/purchase-orders
// @access  Private (Admin only)
const getAllPOs = async (req, res) => {
  try {
    const purchaseOrders = await PurchaseOrder.find()
      .populate('assigned_to', 'name email')
      .sort({ createdAt: -1 });

    res.json(purchaseOrders);
  } catch (error) {
    console.error('Get all POs error:', error);
    res.status(500).json({ message: 'Server error fetching purchase orders' });
  }
};

// @desc    Assign purchase order to trucker
// @route   PUT /api/purchase-orders/:id/assign
// @access  Private (Admin only)
const assignPO = async (req, res) => {
  try {
    const { assigned_to } = req.body;
    const { id } = req.params;

    // Verify the assigned user is a trucker
    const trucker = await User.findById(assigned_to);
    if (!trucker || trucker.role !== 'trucker') {
      return res.status(400).json({ message: 'Invalid trucker ID' });
    }

    const purchaseOrder = await PurchaseOrder.findByIdAndUpdate(
      id,
      { 
        assigned_to,
        status: 'Assigned'
      },
      { new: true }
    ).populate('assigned_to', 'name email');

    if (!purchaseOrder) {
      return res.status(404).json({ message: 'Purchase order not found' });
    }

    res.json(purchaseOrder);
  } catch (error) {
    console.error('Assign PO error:', error);
    res.status(500).json({ message: 'Server error assigning purchase order' });
  }
};

// @desc    Get trucker's assigned purchase orders
// @route   GET /api/purchase-orders/my
// @access  Private (Trucker only)
const getMyPOs = async (req, res) => {
  try {
    const purchaseOrders = await PurchaseOrder.find({
      assigned_to: req.user._id
    }).sort({ createdAt: -1 });

    res.json(purchaseOrders);
  } catch (error) {
    console.error('Get my POs error:', error);
    res.status(500).json({ message: 'Server error fetching your purchase orders' });
  }
};

// @desc    Update purchase order status to completed
// @route   PUT /api/purchase-orders/:id/status
// @access  Private (Trucker only)
const updateStatus = async (req, res) => {
  try {
    const { id } = req.params;

    const purchaseOrder = await PurchaseOrder.findOneAndUpdate(
      {
        _id: id,
        assigned_to: req.user._id,
        status: 'Assigned'
      },
      { status: 'Completed' },
      { new: true }
    );

    if (!purchaseOrder) {
      return res.status(404).json({
        message: 'Purchase order not found or not assigned to you'
      });
    }

    res.json(purchaseOrder);
  } catch (error) {
    console.error('Update status error:', error);
    res.status(500).json({ message: 'Server error updating purchase order status' });
  }
};

// @desc    Get all truckers
// @route   GET /api/purchase-orders/truckers
// @access  Private (Admin only)
const getTruckers = async (req, res) => {
  try {
    const truckers = await User.find({ role: 'trucker' }).select('_id name email');
    res.json(truckers);
  } catch (error) {
    console.error('Get truckers error:', error);
    res.status(500).json({ message: 'Server error fetching truckers' });
  }
};

module.exports = { createPO, getAllPOs, assignPO, getMyPOs, updateStatus, getTruckers };
